/**
 * Mapeador de templates HTML para React Email
 * Converte dados dos DEFAULT_TEMPLATES para props dos componentes React Email
 */

import type { NotificationType, NotificationChannel } from '../../../types/notification-types';
import type { EmailTemplateType, EmailTemplateProps } from './index';

// Interface para dados de template genérico
export interface TemplateData {
  [key: string]: any;
}

// Mapeamento de tipos de notificação para templates React Email
export const NOTIFICATION_TO_EMAIL_TEMPLATE_MAP: Record<string, EmailTemplateType> = {
  // Payment templates
  'payment_reminder_basic': 'payment-reminder',
  'payment_reminder_soon': 'payment-due-soon',
  'payment_reminder_today': 'payment-due-today',
  'payment_overdue_level_1': 'payment-overdue',
  'payment_overdue_level_2': 'payment-overdue',
  'payment_overdue_level_3': 'payment-overdue',
  
  // Class templates
  'class_reminder': 'class-reminder',
  
  // Enrollment templates
  'enrollment_welcome': 'welcome',
  'enrollment_payment_due': 'enrollment-payment-due',
  
  // Event templates
  'event_invitation': 'event-invitation',
  
  // System templates
  'system_notification': 'system-default',
  'admin_alert_overdue': 'admin-alert',
  'admin_alert_attendance': 'admin-alert',
  'admin_alert_system': 'admin-alert',
  
  // Default fallbacks
  'payment': 'payment-reminder',
  'class': 'class-reminder',
  'enrollment': 'welcome',
  'event': 'event-invitation',
  'system': 'system-default'
};

/**
 * Mapeia dados de template genérico para props específicas do React Email
 */
export function mapTemplateDataToProps(
  templateType: EmailTemplateType,
  data: TemplateData
): EmailTemplateProps {
  const baseProps = {
    academyName: data.academyName || 'Academia',
    academyLogo: data.academyLogo,
    primaryColor: data.primaryColor || '#007291',
    secondaryColor: data.secondaryColor || '#004E89'
  };

  switch (templateType) {
    case 'payment-reminder':
      return {
        ...baseProps,
        studentName: data.studentName || data.userName || 'Aluno',
        amount: parseFloat(data.amount) || 0,
        dueDate: data.dueDate || new Date().toISOString(),
        planName: data.planName || 'Plano',
        paymentMethod: data.paymentMethod,
        invoiceUrl: data.invoiceUrl,
        currency: data.currency || 'BRL',
        lateFee: data.lateFee ? parseFloat(data.lateFee) : undefined,
        gracePeriod: data.gracePeriod ? parseInt(data.gracePeriod) : undefined
      };

    case 'payment-due-soon':
      return {
        ...baseProps,
        studentName: data.studentName || data.userName || 'Aluno',
        amount: parseFloat(data.amount) || 0,
        dueDate: data.dueDate || new Date().toISOString(),
        planName: data.planName || 'Plano',
        paymentMethod: data.paymentMethod,
        invoiceUrl: data.invoiceUrl,
        currency: data.currency || 'BRL',
        daysUntilDue: data.daysUntilDue ? parseInt(data.daysUntilDue) : 3
      };

    case 'payment-due-today':
      return {
        ...baseProps,
        studentName: data.studentName || data.userName || 'Aluno',
        amount: parseFloat(data.amount) || 0,
        dueDate: data.dueDate || new Date().toISOString(),
        planName: data.planName || 'Plano',
        paymentMethod: data.paymentMethod,
        invoiceUrl: data.invoiceUrl,
        currency: data.currency || 'BRL',
        gracePeriod: data.gracePeriod ? parseInt(data.gracePeriod) : undefined
      };

    case 'payment-overdue':
      return {
        ...baseProps,
        studentName: data.studentName || data.userName || 'Aluno',
        amount: parseFloat(data.amount) || 0,
        dueDate: data.dueDate || new Date().toISOString(),
        planName: data.planName || 'Plano',
        paymentMethod: data.paymentMethod,
        invoiceUrl: data.invoiceUrl,
        overdueDays: parseInt(data.overdueDays) || 1,
        overdueLevel: determineOverdueLevel(parseInt(data.overdueDays) || 1),
        currency: data.currency || 'BRL',
        lateFee: data.lateFee ? parseFloat(data.lateFee) : undefined,
        suspensionWarning: data.suspensionWarning === true || parseInt(data.overdueDays) >= 15
      };

    case 'welcome':
      return {
        ...baseProps,
        studentName: data.studentName || data.userName || 'Aluno',
        planName: data.planName || 'Plano',
        startDate: data.startDate || new Date().toISOString(),
        enrollmentDate: data.enrollmentDate || new Date().toISOString(),
        instructorName: data.instructorName,
        firstClassDate: data.firstClassDate,
        academyAddress: data.academyAddress,
        academyPhone: data.academyPhone,
        welcomeMessage: data.welcomeMessage,
        dashboardUrl: data.dashboardUrl
      };

    case 'event-invitation':
      return {
        ...baseProps,
        eventName: data.eventName || 'Evento',
        eventDescription: data.eventDescription || 'Descrição do evento',
        eventDate: data.eventDate || new Date().toISOString(),
        eventTime: data.eventTime,
        eventLocation: data.eventLocation || 'Local do evento',
        eventDuration: data.eventDuration,
        studentName: data.studentName || data.userName,
        eventPrice: data.eventPrice ? parseFloat(data.eventPrice) : undefined,
        currency: data.currency || 'BRL',
        registrationUrl: data.registrationUrl,
        registrationDeadline: data.registrationDeadline,
        maxParticipants: data.maxParticipants ? parseInt(data.maxParticipants) : undefined,
        currentParticipants: data.currentParticipants ? parseInt(data.currentParticipants) : undefined,
        eventImage: data.eventImage,
        requirements: data.requirements ? (Array.isArray(data.requirements) ? data.requirements : [data.requirements]) : undefined,
        benefits: data.benefits ? (Array.isArray(data.benefits) ? data.benefits : [data.benefits]) : undefined
      };

    case 'admin-alert':
      return {
        ...baseProps,
        adminName: data.adminName || data.ownerName || 'Administrador',
        alertType: data.alertType || 'custom',
        alertTitle: data.alertTitle || 'Alerta do Sistema',
        alertMessage: data.alertMessage || data.message || 'Mensagem de alerta',
        overdueCount: data.overdueCount ? parseInt(data.overdueCount) : undefined,
        totalAtRisk: data.totalAtRisk ? parseFloat(data.totalAtRisk) : undefined,
        currency: data.currency || 'BRL',
        lowAttendanceCount: data.lowAttendanceCount ? parseInt(data.lowAttendanceCount) : undefined,
        attendanceThreshold: data.attendanceThreshold ? parseInt(data.attendanceThreshold) : undefined,
        actionUrl: data.actionUrl,
        actionLabel: data.actionLabel,
        priority: data.priority || 'medium',
        additionalData: data.additionalData
      };

    case 'class-reminder':
      return {
        ...baseProps,
        studentName: data.studentName || data.userName || 'Aluno',
        className: data.className || 'Aula',
        classDate: data.classDate || new Date().toISOString(),
        classTime: data.classTime || '00:00',
        instructorName: data.instructorName,
        classLocation: data.classLocation,
        classDuration: data.classDuration,
        classDescription: data.classDescription,
        requirements: data.requirements ? (Array.isArray(data.requirements) ? data.requirements : [data.requirements]) : undefined
      };

    // Fallback para outros templates
    default:
      return {
        ...baseProps,
        title: data.title || 'Notificação',
        message: data.message || 'Mensagem do sistema'
      } as any;
  }
}

/**
 * Determina o nível de atraso baseado nos dias
 */
function determineOverdueLevel(overdueDays: number): 1 | 2 | 3 {
  if (overdueDays <= 7) return 1;
  if (overdueDays <= 14) return 2;
  return 3;
}

/**
 * Obtém o template React Email apropriado baseado no tipo de notificação
 */
export function getEmailTemplateType(
  notificationType: NotificationType,
  templateName?: string,
  overdueDays?: number
): EmailTemplateType {
  // Se um nome específico de template foi fornecido, tenta mapear diretamente
  if (templateName && NOTIFICATION_TO_EMAIL_TEMPLATE_MAP[templateName]) {
    return NOTIFICATION_TO_EMAIL_TEMPLATE_MAP[templateName];
  }

  // Lógica específica para pagamentos baseada em dias de atraso
  if (notificationType === 'payment') {
    if (overdueDays && overdueDays > 0) {
      return 'payment-overdue';
    }
    if (templateName?.includes('today')) {
      return 'payment-due-today';
    }
    if (templateName?.includes('soon') || templateName?.includes('3')) {
      return 'payment-due-soon';
    }
    return 'payment-reminder';
  }

  // Mapeamento padrão por tipo
  const defaultMap: Record<NotificationType, EmailTemplateType> = {
    payment: 'payment-reminder',
    class: 'class-reminder',
    enrollment: 'welcome',
    enrollment_payment_due: 'enrollment-payment-due',
    expense_due: 'expense-due',
    new_enrollment_notification: 'new-enrollment-notification',
    event: 'event-invitation',
    system: 'system-default'
  };

  return defaultMap[notificationType] || 'system-default';
}

/**
 * Função principal para converter template HTML em React Email
 */
export function convertToReactEmailTemplate(
  notificationType: NotificationType,
  templateData: TemplateData,
  templateName?: string
): {
  templateType: EmailTemplateType;
  props: EmailTemplateProps;
} {
  const templateType = getEmailTemplateType(
    notificationType,
    templateName,
    templateData.overdueDays ? parseInt(templateData.overdueDays) : undefined
  );

  const props = mapTemplateDataToProps(templateType, templateData);

  return {
    templateType,
    props
  };
}
