/**
 * Serviço de configuração centralizada para notificações
 * Gerencia configurações do SaaS e específicas por tenant
 */

import { createClient } from '@/services/supabase/server';

export interface SaaSNotificationConfig {
  email: {
    provider: 'resend' | 'aws_ses';
    resend: {
      apiKey: string;
      fromDomain: string;
      webhookSecret: string;
    };
    aws: {
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
      fromDomain: string;
    };
  };
  whatsapp: {
    apiUrl: string;
    apiKey: string;
    instanceName: string;
    webhookSecret: string;
  };
}

export interface TenantNotificationConfig {
  tenantId: string;
  tenantName: string;
  tenantSlug: string;
  emailFromDomain: string;
  emailFromName: string;
  emailEnabled: boolean;
  whatsappEnabled: boolean;
  whatsappOptInMessage?: string;
  notificationTypes: Record<string, any>;
  quietHours: {
    start: string;
    end: string;
    timezone: string;
  };
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
}

export interface CreateTenantNotificationSettingsData {
  tenantId: string;
  emailFromName?: string;
  emailEnabled?: boolean;
  whatsappEnabled?: boolean;
  whatsappOptInMessage?: string;
  notificationTypes?: Record<string, any>;
  quietHoursStart?: string;
  quietHoursEnd?: string;
  timezone?: string;
}

export interface UpdateTenantNotificationSettingsData {
  emailFromName?: string;
  emailEnabled?: boolean;
  whatsappEnabled?: boolean;
  whatsappOptInMessage?: string;
  notificationTypes?: Record<string, any>;
  quietHoursStart?: string;
  quietHoursEnd?: string;
  timezone?: string;
}

export class NotificationConfigService {
  /**
   * Obtém as configurações do SaaS (vêm do ambiente)
   */
  private static getSaaSConfig(): SaaSNotificationConfig {
    return {
      email: {
        provider: (process.env.EMAIL_PROVIDER as 'resend' | 'aws_ses') || 'resend',
        resend: {
          apiKey: process.env.RESEND_API_KEY || '',
          fromDomain: process.env.RESEND_FROM_DOMAIN || 'sondtheanime.site',
          webhookSecret: process.env.RESEND_WEBHOOK_SECRET || ''
        },
        aws: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
          region: process.env.AWS_REGION || 'us-east-1',
          fromDomain: process.env.AWS_SES_FROM_DOMAIN || 'sondtheanime.site'
        }
      },
      whatsapp: {
        apiUrl: process.env.EVOLUTION_API_URL || '',
        apiKey: process.env.EVOLUTION_API_KEY || '',
        instanceName: process.env.EVOLUTION_INSTANCE_NAME || 'apexsaas-whatsapp',
        webhookSecret: process.env.EVOLUTION_WEBHOOK_SECRET || ''
      }
    };
  }

  /**
   * Obtém as configurações específicas do tenant
   */
  async getTenantConfig(tenantId: string): Promise<TenantNotificationConfig> {
    try {
      console.log('🔍 [DEBUG] NotificationConfigService.getTenantConfig() iniciado para tenant:', tenantId);

      const supabase = await createClient();
      console.log('🔍 [DEBUG] Cliente Supabase criado');

      // Buscar dados do tenant
      console.log('🔍 [DEBUG] Buscando dados do tenant...');
      const { data: tenant, error: tenantError } = await supabase
        .from('tenants')
        .select('name, slug, logo_url, primary_color, secondary_color')
        .eq('id', tenantId)
        .single();

      if (tenantError) {
        console.error('🔍 [DEBUG] Erro ao buscar tenant:', tenantError);
        throw new Error(`Tenant não encontrado: ${tenantError.message}`);
      }
      console.log('🔍 [DEBUG] Tenant encontrado:', tenant);

      // Buscar configurações de notificação do tenant
      console.log('🔍 [DEBUG] Buscando configurações de notificação...');
      const { data: settings, error: settingsError } = await supabase
        .from('tenant_notification_settings')
        .select('*')
        .eq('tenant_id', tenantId)
        .single();

      console.log('🔍 [DEBUG] Resultado da busca de configurações:', {
        hasData: !!settings,
        hasError: !!settingsError,
        errorCode: settingsError?.code,
        errorMessage: settingsError?.message
      });

      // Se não existir configuração, criar uma padrão
      if (settingsError && settingsError.code === 'PGRST116') {
        console.log('🔍 [DEBUG] Configurações não encontradas, criando padrão...');
        await this.createDefaultSettings(tenantId);

        // Buscar novamente após criar
        const { data: newSettings } = await supabase
          .from('tenant_notification_settings')
          .select('*')
          .eq('tenant_id', tenantId)
          .single();

        console.log('🔍 [DEBUG] Configurações padrão criadas:', !!newSettings);
        return this.buildTenantConfig(tenant, newSettings);
      }

      if (settingsError) {
        console.error('🔍 [DEBUG] Erro ao buscar configurações de notificação:', settingsError);
        throw new Error(`Erro ao buscar configurações: ${settingsError.message}`);
      }

      console.log('🔍 [DEBUG] Configurações encontradas, construindo config...');
      return this.buildTenantConfig(tenant, settings);

    } catch (error) {
      console.error('🔍 [DEBUG] Erro capturado no getTenantConfig:', error);
      throw error;
    }
  }

  /**
   * Cria configurações padrão para um tenant
   */
  async createDefaultSettings(tenantId: string): Promise<void> {
    try {
      const supabase = await createClient();

      // Buscar dados básicos do tenant para gerar configurações
      const { data: tenant } = await supabase
        .from('tenants')
        .select('name, slug')
        .eq('id', tenantId)
        .single();

      const defaultSettings = {
        tenant_id: tenantId,
        email_from_domain: `${tenant?.slug || 'academia'}@sondtheanime.site`,
        email_from_name: tenant?.name || 'Academia',
        email_enabled: true,
        whatsapp_enabled: true,
        notification_types: {
          payment: { email: true, whatsapp: false, in_app: true },
          class: { email: true, whatsapp: false, in_app: true },
          system: { email: false, whatsapp: false, in_app: true },
          enrollment: { email: true, whatsapp: false, in_app: true },
          event: { email: true, whatsapp: false, in_app: true }
        },
        quiet_hours_start: '22:00:00',
        quiet_hours_end: '08:00:00',
        timezone: 'America/Sao_Paulo'
      };

      const { error } = await supabase
        .from('tenant_notification_settings')
        .insert(defaultSettings);

      if (error) {
        console.error('Erro ao criar configurações padrão:', error);
        throw new Error(`Erro ao criar configurações padrão: ${error.message}`);
      }

    } catch (error) {
      console.error('Erro ao criar configurações padrão:', error);
      throw error;
    }
  }

  /**
   * Atualiza as configurações de notificação de um tenant
   */
  async updateTenantSettings(tenantId: string, data: UpdateTenantNotificationSettingsData): Promise<void> {
    try {
      const supabase = await createClient();

      // Transformar camelCase para snake_case para compatibilidade com o banco
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Mapear propriedades camelCase para snake_case
      if (data.emailFromName !== undefined) {
        updateData.email_from_name = data.emailFromName;
      }
      if (data.emailEnabled !== undefined) {
        updateData.email_enabled = data.emailEnabled;
      }
      if (data.whatsappEnabled !== undefined) {
        updateData.whatsapp_enabled = data.whatsappEnabled;
      }
      if (data.whatsappOptInMessage !== undefined) {
        updateData.whatsapp_opt_in_message = data.whatsappOptInMessage;
      }
      if (data.notificationTypes !== undefined) {
        updateData.notification_types = data.notificationTypes;
      }
      if (data.quietHoursStart !== undefined) {
        updateData.quiet_hours_start = data.quietHoursStart;
      }
      if (data.quietHoursEnd !== undefined) {
        updateData.quiet_hours_end = data.quietHoursEnd;
      }
      if (data.timezone !== undefined) {
        updateData.timezone = data.timezone;
      }

      const { error } = await supabase
        .from('tenant_notification_settings')
        .update(updateData)
        .eq('tenant_id', tenantId);

      if (error) {
        console.error('Erro ao atualizar configurações:', error);
        throw new Error(`Erro ao atualizar configurações: ${error.message}`);
      }

    } catch (error) {
      console.error('Erro ao atualizar configurações do tenant:', error);
      throw error;
    }
  }

  /**
   * Constrói o objeto de configuração do tenant
   */
  private buildTenantConfig(tenant: any, settings: any): TenantNotificationConfig {
    return {
      tenantId: tenant.id || settings.tenant_id,
      tenantName: tenant?.name || 'Academia',
      tenantSlug: tenant?.slug || 'academia',
      emailFromDomain: settings?.email_from_domain || `${tenant?.slug || 'academia'}@sondtheanime.site`,
      emailFromName: settings?.email_from_name || tenant?.name || 'Academia',
      emailEnabled: settings?.email_enabled ?? true,
      whatsappEnabled: settings?.whatsapp_enabled ?? true,
      whatsappOptInMessage: settings?.whatsapp_opt_in_message,
      notificationTypes: settings?.notification_types || {},
      quietHours: {
        start: settings?.quiet_hours_start || '22:00:00',
        end: settings?.quiet_hours_end || '08:00:00',
        timezone: settings?.timezone || 'America/Sao_Paulo'
      },
      academyLogo: tenant?.logo_url,
      primaryColor: tenant?.primary_color,
      secondaryColor: tenant?.secondary_color
    };
  }

  /**
   * Obtém as configurações do SaaS
   */
  static getGlobalConfig(): SaaSNotificationConfig {
    return this.getSaaSConfig();
  }

  /**
   * Valida se as configurações globais estão corretas
   */
  static validateGlobalConfig(): { isValid: boolean; errors: string[] } {
    const config = this.getSaaSConfig();
    const errors: string[] = [];

    // Validar configurações de e-mail
    if (config.email.provider === 'resend') {
      if (!config.email.resend.apiKey) {
        errors.push('RESEND_API_KEY não configurada');
      }
      if (!config.email.resend.fromDomain) {
        errors.push('RESEND_FROM_DOMAIN não configurado');
      }
    }

    if (config.email.provider === 'aws_ses') {
      if (!config.email.aws.accessKeyId) {
        errors.push('AWS_ACCESS_KEY_ID não configurada');
      }
      if (!config.email.aws.secretAccessKey) {
        errors.push('AWS_SECRET_ACCESS_KEY não configurada');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
