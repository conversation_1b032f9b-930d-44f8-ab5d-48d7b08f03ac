/**
 * Dispatcher de notificações multi-canal
 * Coordena o envio de notificações através de diferentes canais
 */

import { NotificationChannelFactory } from './notification-channel-factory';
import { NotificationService } from '../core/notification-service';
import { NotificationPermissionService } from '../core/notification-permission-service';
import type {
  NotificationChannel,
  NotificationType,
  NotificationCategory,
  NotificationPriority,
  CreateNotificationData
} from '../types/notification-types';
import type { NotificationChannelData, NotificationChannelResult } from './base/notification-channel';

export interface DispatchNotificationData {
  tenantId: string;
  userId: string;
  type: NotificationType;
  category: NotificationCategory;
  priority?: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: NotificationChannel[];
  templateId?: string;
  variables?: Record<string, any>;
  scheduled_for?: string;
  expires_at?: string;
}

export interface DispatchResult {
  success: boolean;
  notificationId?: string;
  channelResults: Record<NotificationChannel, NotificationChannelResult>;
  errors: string[];
}

export class NotificationDispatcher {
  private notificationService: NotificationService;
  private permissionService: NotificationPermissionService;

  constructor() {
    this.notificationService = new NotificationService();
    this.permissionService = new NotificationPermissionService();
  }

  /**
   * Envia uma notificação através de múltiplos canais
   */
  async dispatch(data: DispatchNotificationData): Promise<DispatchResult> {
    const channelResults: Record<string, NotificationChannelResult> = {};
    const errors: string[] = [];
    let notificationId: string | undefined;

    try {
      // 1. Criar notificação in-app no banco de dados
      const notificationData: CreateNotificationData = {
        user_id: data.userId,
        type: data.type,
        category: data.category,
        priority: data.priority || 'medium',
        title: data.title,
        message: data.message,
        data: data.data,
        channels: data.channels || ['in_app'],
        scheduled_for: data.scheduled_for,
        expires_at: data.expires_at
      };

      const createResult = await this.notificationService.create(data.tenantId, notificationData);
      
      if (!createResult.success) {
        errors.push(`Erro ao criar notificação: ${createResult.error}`);
        return {
          success: false,
          channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
          errors
        };
      }

      notificationId = createResult.data?.id;

      // 2. Determinar canais a serem usados
      // Se canais foram especificados, assumir que já foram verificados
      // Caso contrário, aplicar verificação de permissões
      let channels: NotificationChannel[];

      if (data.channels && data.channels.length > 0) {
        // Canais já foram especificados (provavelmente já verificados)
        channels = data.channels;
      } else {
        // Aplicar verificação de permissões para canais padrão
        const requestedChannels: NotificationChannel[] = ['in_app'];
        channels = await this.permissionService.getPermittedChannels(
          data.tenantId,
          data.userId,
          data.type,
          requestedChannels
        );
      }

      // 3. Preparar dados para os canais
      const channelData: NotificationChannelData = {
        tenantId: data.tenantId,
        userId: data.userId,
        type: data.type,
        title: data.title,
        message: data.message,
        data: data.data,
        templateId: data.templateId,
        variables: data.variables
      };

      // 4. Verificar se há canais permitidos
      if (channels.length === 0) {
        errors.push('Nenhum canal de notificação permitido para este usuário/tipo');
        return {
          success: false,
          notificationId,
          channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
          errors
        };
      }

      // 5. Enviar através de cada canal permitido
      for (const channelType of channels) {
        try {
          // Pular canal in-app pois já foi criado no banco
          if (channelType === 'in_app') {
            channelResults[channelType] = {
              success: true,
              messageId: notificationId
            };
            continue;
          }

          // Obter canal e enviar
          const channel = await NotificationChannelFactory.getChannel(channelType);
          const result = await channel.send(channelData);
          
          channelResults[channelType] = result;

          if (!result.success && result.error) {
            errors.push(`Erro no canal ${channelType}: ${result.error}`);
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
          channelResults[channelType] = {
            success: false,
            error: errorMessage
          };
          errors.push(`Erro no canal ${channelType}: ${errorMessage}`);
        }
      }

      // 6. Determinar sucesso geral
      const hasSuccessfulChannel = Object.values(channelResults).some(result => result.success);

      return {
        success: hasSuccessfulChannel,
        notificationId,
        channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      errors.push(`Erro geral: ${errorMessage}`);
      
      return {
        success: false,
        notificationId,
        channelResults: channelResults as Record<NotificationChannel, NotificationChannelResult>,
        errors
      };
    }
  }

  /**
   * Envia múltiplas notificações em lote
   */
  async dispatchBatch(notifications: DispatchNotificationData[]): Promise<DispatchResult[]> {
    const results: DispatchResult[] = [];

    // Processar em paralelo com limite de concorrência
    const batchSize = 10;
    for (let i = 0; i < notifications.length; i += batchSize) {
      const batch = notifications.slice(i, i + batchSize);
      
      const batchPromises = batch.map(notification => this.dispatch(notification));
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            channelResults: {} as Record<NotificationChannel, NotificationChannelResult>,
            errors: [`Erro no lote: ${result.reason}`]
          });
        }
      });
    }

    return results;
  }

  /**
   * Envia notificação de pagamento
   */
  async sendPaymentReminder(data: {
    tenantId: string;
    userId: string;
    studentName: string;
    amount: number;
    dueDate: string;
    planName: string;
    paymentMethod?: string;
    invoiceUrl?: string;
    channels?: NotificationChannel[];
  }): Promise<DispatchResult> {
    console.log('🔍 [DEBUG] NotificationDispatcher.sendPaymentReminder() iniciado');
    console.log('🔍 [DEBUG] Dados recebidos:', {
      tenantId: data.tenantId,
      userId: data.userId,
      studentName: data.studentName,
      amount: data.amount,
      dueDate: data.dueDate,
      planName: data.planName,
      channels: data.channels
    });

    // Determinar canais solicitados (padrão: in_app e email)
    const requestedChannels = data.channels || ['in_app', 'email'];
    console.log('🔍 [DEBUG] Canais solicitados:', requestedChannels);

    // Verificar canais permitidos baseado nas configurações
    console.log('🔍 [DEBUG] Verificando canais permitidos...');
    const permittedChannels = await this.permissionService.getPermittedChannels(
      data.tenantId,
      data.userId,
      'payment',
      requestedChannels
    );
    console.log('🔍 [DEBUG] Canais permitidos:', permittedChannels);

    return await this.dispatch({
      tenantId: data.tenantId,
      userId: data.userId,
      type: 'payment',
      category: 'reminder',
      priority: 'high',
      title: 'Lembrete de Pagamento',
      message: `Sua mensalidade de ${data.planName} vence em breve.`,
      channels: permittedChannels,
      templateId: 'payment_reminder',
      variables: {
        studentName: data.studentName,
        amount: data.amount,
        dueDate: data.dueDate,
        planName: data.planName,
        paymentMethod: data.paymentMethod,
        invoiceUrl: data.invoiceUrl
      }
    });
  }

  /**
   * Envia notificação de aula
   */
  async sendClassReminder(data: {
    tenantId: string;
    userId: string;
    studentName: string;
    className: string;
    instructorName: string;
    classDate: string;
    classTime: string;
    location?: string;
    channels?: NotificationChannel[];
    reminderType?: 'upcoming' | 'today' | 'starting_soon';
  }): Promise<DispatchResult> {
    // Determinar canais solicitados (padrão: in_app e email)
    const requestedChannels = data.channels || ['in_app', 'email'];

    // Verificar canais permitidos baseado nas configurações
    const permittedChannels = await this.permissionService.getPermittedChannels(
      data.tenantId,
      data.userId,
      'class',
      requestedChannels
    );

    return await this.dispatch({
      tenantId: data.tenantId,
      userId: data.userId,
      type: 'class',
      category: 'reminder',
      priority: data.reminderType === 'starting_soon' ? 'urgent' : 'medium',
      title: 'Lembrete de Aula',
      message: `Você tem uma aula de ${data.className} agendada.`,
      channels: permittedChannels,
      templateId: 'class_reminder',
      variables: {
        studentName: data.studentName,
        className: data.className,
        instructorName: data.instructorName,
        classDate: data.classDate,
        classTime: data.classTime,
        location: data.location,
        reminderType: data.reminderType || 'upcoming'
      }
    });
  }

  /**
   * Envia notificação do sistema
   */
  async sendSystemNotification(data: {
    tenantId: string;
    userId: string;
    title: string;
    message: string;
    category?: NotificationCategory;
    priority?: NotificationPriority;
    channels?: NotificationChannel[];
  }): Promise<DispatchResult> {
    return await this.dispatch({
      tenantId: data.tenantId,
      userId: data.userId,
      type: 'system',
      category: data.category || 'info',
      priority: data.priority || 'medium',
      title: data.title,
      message: data.message,
      channels: data.channels || ['in_app']
    });
  }

  /**
   * Verifica o status de entrega de uma notificação
   */
  async getDeliveryStatus(notificationId: string, channelType: NotificationChannel): Promise<{
    success: boolean;
    status?: string;
    error?: string;
  }> {
    try {
      const channel = await NotificationChannelFactory.getChannel(channelType);
      const status = await channel.getDeliveryStatus(notificationId);
      
      return {
        success: true,
        status: status.status
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }
}
